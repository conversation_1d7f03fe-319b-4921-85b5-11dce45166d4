# Tyler Hipolito Portfolio - Design System

## Design Philosophy
Inspired by aboutluca.com, this portfolio emphasizes **sophisticated minimalism** with **purposeful animations** that showcase technical expertise while maintaining professional elegance.

## Color Palette

### Primary Neutral Colors
```css
/* Light Theme */
--background: #ffffff
--foreground: #0a0a0a
--muted: #f5f5f5
--muted-foreground: #737373
--border: #e5e5e5
--accent: #f5f5f5
--accent-foreground: #0a0a0a

/* Dark Theme Support */
--dark-background: #0a0a0a
--dark-foreground: #fafafa
--dark-muted: #262626
--dark-muted-foreground: #a3a3a3
--dark-border: #262626
--dark-accent: #262626
--dark-accent-foreground: #fafafa
```

### Accent Colors (Minimal Usage)
```css
--primary: #18181b      /* Deep charcoal for emphasis */
--secondary: #71717a    /* Medium gray for secondary text */
--success: #22c55e      /* Subtle green for success states */
--warning: #f59e0b      /* Amber for warnings */
--error: #ef4444        /* Red for errors */
```

## Typography

### Font Stack
- **Primary**: <PERSON>eist Sans (already configured)
- **Monospace**: Geist Mono (for code snippets)
- **Fallback**: system-ui, -apple-system, sans-serif

### Type Scale
```css
/* Headings */
--text-6xl: 3.75rem     /* 60px - Hero titles */
--text-5xl: 3rem        /* 48px - Section titles */
--text-4xl: 2.25rem     /* 36px - Page titles */
--text-3xl: 1.875rem    /* 30px - Subsection titles */
--text-2xl: 1.5rem      /* 24px - Card titles */
--text-xl: 1.25rem      /* 20px - Large text */

/* Body Text */
--text-lg: 1.125rem     /* 18px - Large body */
--text-base: 1rem       /* 16px - Default body */
--text-sm: 0.875rem     /* 14px - Small text */
--text-xs: 0.75rem      /* 12px - Captions */
```

### Font Weights
- **Light**: 300 (minimal usage)
- **Regular**: 400 (body text)
- **Medium**: 500 (emphasis)
- **Semibold**: 600 (headings)
- **Bold**: 700 (strong emphasis)

## Spacing System

### Base Unit: 4px (0.25rem)
```css
--space-1: 0.25rem      /* 4px */
--space-2: 0.5rem       /* 8px */
--space-3: 0.75rem      /* 12px */
--space-4: 1rem         /* 16px */
--space-5: 1.25rem      /* 20px */
--space-6: 1.5rem       /* 24px */
--space-8: 2rem         /* 32px */
--space-10: 2.5rem      /* 40px */
--space-12: 3rem        /* 48px */
--space-16: 4rem        /* 64px */
--space-20: 5rem        /* 80px */
--space-24: 6rem        /* 96px */
--space-32: 8rem        /* 128px */
```

## Layout & Grid

### Container Sizes
- **Mobile**: 100% with 1rem padding
- **Tablet**: 768px max-width
- **Desktop**: 1200px max-width
- **Large**: 1400px max-width

### Section Spacing
- **Section padding**: 5rem (mobile: 3rem)
- **Component spacing**: 3rem (mobile: 2rem)
- **Element spacing**: 1.5rem (mobile: 1rem)

## Animation Principles

### Core Animation Values
```css
/* Timing Functions */
--ease-out: cubic-bezier(0.16, 1, 0.3, 1)
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
--ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1)

/* Durations */
--duration-fast: 150ms
--duration-normal: 300ms
--duration-slow: 500ms
--duration-slower: 700ms
```

### Animation Types

#### 1. **Entrance Animations**
- **Fade In Up**: Elements slide up 20px while fading in
- **Stagger**: Sequential animation with 100ms delays
- **Scale In**: Subtle scale from 0.95 to 1.0

#### 2. **Scroll Animations**
- **Parallax**: Background elements move slower than foreground
- **Reveal**: Elements animate in when 20% visible
- **Progress**: Progress bars and counters animate on scroll

#### 3. **Interaction Animations**
- **Hover Lift**: Subtle 2px translate-y on hover
- **Button Press**: Scale down to 0.98 on active
- **Link Underline**: Animated underline expansion

#### 4. **Page Transitions**
- **Smooth Scroll**: Custom smooth scrolling between sections
- **Route Transitions**: Fade between pages with 300ms duration

## Component Specifications

### Navigation
- **Height**: 80px (mobile: 64px)
- **Background**: Translucent with backdrop blur
- **Animation**: Slide down on scroll up, hide on scroll down
- **Mobile**: Hamburger menu with slide-in drawer

### Hero Section
- **Height**: 100vh (minimum 600px)
- **Typography**: Large, bold statements with animated reveals
- **CTA**: Prominent button with hover animations

### Cards
- **Border Radius**: 12px
- **Shadow**: Subtle, increases on hover
- **Padding**: 2rem (mobile: 1.5rem)
- **Hover**: Lift effect with shadow enhancement

### Buttons
- **Primary**: Dark background, white text
- **Secondary**: Transparent with border
- **Border Radius**: 8px
- **Padding**: 0.75rem 1.5rem
- **Hover**: Smooth color transitions

## Interactive Elements

### Scroll-Triggered Animations
1. **Section Reveals**: Fade in up when 20% visible
2. **Counter Animations**: Numbers count up on scroll
3. **Progress Bars**: Fill animation on scroll
4. **Image Parallax**: Subtle background movement

### Hover Effects
1. **Project Cards**: Lift + shadow + image scale
2. **Skill Items**: Glow effect + slight rotation
3. **Social Links**: Icon color change + bounce
4. **Navigation**: Underline animation

### Loading States
1. **Skeleton Screens**: Animated placeholders
2. **Progressive Enhancement**: Content loads gracefully
3. **Image Loading**: Blur-to-sharp transitions

## Responsive Breakpoints

```css
/* Mobile First Approach */
--mobile: 0px
--tablet: 768px
--desktop: 1024px
--large: 1280px
--xl: 1536px
```

## Accessibility Standards

### Focus States
- **Visible**: 2px solid outline with offset
- **Color**: High contrast focus ring
- **Animation**: Smooth focus transitions

### Motion Preferences
- **Respect**: prefers-reduced-motion
- **Fallback**: Instant transitions for sensitive users
- **Toggle**: Optional motion disable control

## Performance Guidelines

### Animation Performance
- **Use**: transform and opacity for animations
- **Avoid**: animating layout properties
- **GPU**: will-change for complex animations
- **Cleanup**: Remove will-change after animation

### Loading Strategy
- **Critical**: Inline critical CSS
- **Fonts**: Preload font files
- **Images**: Lazy load with intersection observer
- **Code**: Split by route for optimal loading

---

This design system will ensure consistency across all components while creating a portfolio that truly stands out through thoughtful animations and interactions.
