'use client';

import React, { useEffect, useRef, useState } from 'react';

interface TrailPoint {
  x: number;
  y: number;
  timestamp: number;
  id: number;
}

interface SimpleCursorTrailProps {
  className?: string;
  trailLength?: number;
  particleLifespan?: number;
}

export const SimpleCursorTrail: React.FC<SimpleCursorTrailProps> = ({
  className = '',
  trailLength = 20,
  particleLifespan = 2000,
}) => {
  const [trail, setTrail] = useState<TrailPoint[]>([]);
  const [isMoving, setIsMoving] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const idCounter = useRef(0);

  useEffect(() => {
    let mouseTimer: NodeJS.Timeout;

    const handleMouseMove = (e: MouseEvent) => {
      const container = containerRef.current;
      if (!container) return;

      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Only add points if mouse is within container
      if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
        setIsMoving(true);
        clearTimeout(mouseTimer);
        mouseTimer = setTimeout(() => setIsMoving(false), 100);

        const newPoint: TrailPoint = {
          x,
          y,
          timestamp: Date.now(),
          id: idCounter.current++,
        };

        setTrail(prev => {
          const updated = [...prev, newPoint];
          // Keep only recent points
          return updated.slice(-trailLength);
        });
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();
      const touch = e.touches[0];
      if (touch) {
        const mouseEvent = new MouseEvent('mousemove', {
          clientX: touch.clientX,
          clientY: touch.clientY,
        });
        handleMouseMove(mouseEvent);
      }
    };

    // Clean up old trail points
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      setTrail(prev => prev.filter(point => now - point.timestamp < particleLifespan));
    }, 100);

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('touchmove', handleTouchMove);
      clearInterval(cleanupInterval);
      clearTimeout(mouseTimer);
    };
  }, [trailLength, particleLifespan]);

  return (
    <div ref={containerRef} className={`absolute inset-0 pointer-events-none ${className}`}>
      {trail.map((point, index) => {
        const age = Date.now() - point.timestamp;
        const lifeRatio = age / particleLifespan;
        const opacity = Math.max(0, 1 - lifeRatio);
        const size = Math.max(2, 8 * (1 - lifeRatio));
        
        return (
          <div
            key={point.id}
            className="absolute rounded-full bg-white pointer-events-none"
            style={{
              left: point.x - size / 2,
              top: point.y - size / 2,
              width: size,
              height: size,
              opacity: opacity * 0.8,
              boxShadow: `0 0 ${size * 2}px rgba(255, 255, 255, ${opacity * 0.5})`,
              transform: `scale(${1 - lifeRatio * 0.5})`,
              transition: 'all 0.1s ease-out',
            }}
          />
        );
      })}
    </div>
  );
};
