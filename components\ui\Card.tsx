'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { CardProps } from '@/lib/types';
import { hoverLift, fadeInUp } from '@/lib/animations';

const cardVariants = {
  default: 'bg-background border border-border',
  elevated: 'bg-background shadow-md hover:shadow-lg',
  bordered: 'bg-background border-2 border-border',
};

const cardPadding = {
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
};

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  hover = true,
  padding = 'md',
  className,
  ...props
}) => {
  const baseClasses = cn(
    'rounded-xl transition-all duration-300 ease-out',
    cardVariants[variant],
    cardPadding[padding],
    className
  );

  const motionProps = hover
    ? {
        variants: hoverLift,
        initial: 'rest',
        whileHover: 'hover',
        transition: {
          duration: 0.3,
          ease: 'easeOut',
        },
      }
    : {};

  return (
    <motion.div
      className={baseClasses}
      {...motionProps}
      {...props}
    >
      {children}
    </motion.div>
  );
};

// Card Header
export const CardHeader: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('mb-4', className)}>
      {children}
    </div>
  );
};

// Card Title
export const CardTitle: React.FC<{
  children: React.ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}> = ({ children, className, as: Component = 'h3' }) => {
  return (
    <Component className={cn('text-xl font-semibold text-foreground', className)}>
      {children}
    </Component>
  );
};

// Card Description
export const CardDescription: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <p className={cn('text-muted-foreground', className)}>
      {children}
    </p>
  );
};

// Card Content
export const CardContent: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('', className)}>
      {children}
    </div>
  );
};

// Card Footer
export const CardFooter: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('mt-4 pt-4 border-t border-border', className)}>
      {children}
    </div>
  );
};

// Project Card (specialized card for projects)
export const ProjectCard: React.FC<{
  title: string;
  description: string;
  image?: string;
  tags?: string[];
  href?: string;
  className?: string;
  children?: React.ReactNode;
}> = ({ title, description, image, tags, href, className, children }) => {
  const CardComponent = href ? motion.a : motion.div;
  const cardProps = href ? { href } : {};

  return (
    <CardComponent
      className={cn(
        'group cursor-pointer overflow-hidden',
        className
      )}
      variants={hoverLift}
      initial="rest"
      whileHover="hover"
      {...cardProps}
    >
      <Card hover={false} className="h-full">
        {image && (
          <div className="relative mb-4 overflow-hidden rounded-lg">
            <motion.img
              src={image}
              alt={title}
              className="h-48 w-full object-cover transition-transform duration-300 group-hover:scale-105"
              loading="lazy"
            />
          </div>
        )}
        
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>

        {tags && tags.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <motion.span
                key={tag}
                className="rounded-full bg-accent px-3 py-1 text-xs font-medium text-accent-foreground"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {tag}
              </motion.span>
            ))}
          </div>
        )}

        {children}
      </Card>
    </CardComponent>
  );
};

// Skill Card (specialized card for skills)
export const SkillCard: React.FC<{
  name: string;
  level: number;
  icon?: React.ReactNode;
  color?: string;
  className?: string;
}> = ({ name, level, icon, color, className }) => {
  return (
    <Card
      className={cn('text-center', className)}
      hover={true}
    >
      {icon && (
        <div className="mb-3 flex justify-center">
          <div
            className="rounded-lg p-3"
            style={{ backgroundColor: color ? `${color}20` : undefined }}
          >
            {icon}
          </div>
        </div>
      )}
      
      <CardTitle className="mb-2 text-lg">{name}</CardTitle>
      
      <div className="mb-2">
        <div className="h-2 w-full rounded-full bg-muted">
          <motion.div
            className="h-2 rounded-full"
            style={{ backgroundColor: color || '#3b82f6' }}
            initial={{ width: 0 }}
            animate={{ width: `${level}%` }}
            transition={{ duration: 1, delay: 0.2 }}
          />
        </div>
      </div>
      
      <p className="text-sm text-muted-foreground">{level}%</p>
    </Card>
  );
};

// Animated Card Grid
export const CardGrid: React.FC<{
  children: React.ReactNode;
  className?: string;
  columns?: 1 | 2 | 3 | 4;
}> = ({ children, className, columns = 3 }) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  return (
    <motion.div
      className={cn(
        'grid gap-6',
        gridCols[columns],
        className
      )}
      variants={{
        hidden: {},
        visible: {
          transition: {
            staggerChildren: 0.1,
          },
        },
      }}
      initial="hidden"
      animate="visible"
    >
      {React.Children.map(children, (child, index) => (
        <motion.div
          key={index}
          variants={fadeInUp}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

export default Card;
