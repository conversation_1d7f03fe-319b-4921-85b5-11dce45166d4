# Component Architecture - Tyler Hipolito Portfolio

## Project Structure

```
portfolio/
├── app/                          # Next.js App Router
│   ├── (sections)/              # Route groups for sections
│   │   ├── about/
│   │   ├── projects/
│   │   ├── experience/
│   │   └── contact/
│   ├── globals.css              # Global styles & design system
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Home page
├── components/                   # Reusable components
│   ├── ui/                      # Base UI components
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   ├── Input.tsx
│   │   ├── Modal.tsx
│   │   └── index.ts
│   ├── layout/                  # Layout components
│   │   ├── Header.tsx
│   │   ├── Navigation.tsx
│   │   ├── Footer.tsx
│   │   └── MobileMenu.tsx
│   ├── sections/                # Page sections
│   │   ├── Hero.tsx
│   │   ├── About.tsx
│   │   ├── Projects.tsx
│   │   ├── Skills.tsx
│   │   ├── Experience.tsx
│   │   └── Contact.tsx
│   └── animations/              # Animation components
│       ├── ScrollReveal.tsx
│       ├── ParallaxSection.tsx
│       ├── TypewriterText.tsx
│       └── CounterAnimation.tsx
├── lib/                         # Utilities & configurations
│   ├── animations.ts            # Animation definitions
│   ├── utils.ts                 # Utility functions
│   ├── constants.ts             # App constants
│   └── types.ts                 # TypeScript types
├── hooks/                       # Custom React hooks
│   ├── useScrollAnimation.ts
│   ├── useIntersectionObserver.ts
│   └── useParallax.ts
└── public/                      # Static assets
    ├── images/
    ├── icons/
    └── documents/
```

## Component Categories

### 1. UI Components (`components/ui/`)
Base reusable components following our design system.

#### Button Component
- **Variants**: primary, secondary, ghost, link
- **Sizes**: sm, md, lg
- **States**: default, hover, active, disabled, loading
- **Animations**: hover scale, press effect

#### Card Component
- **Variants**: default, elevated, bordered
- **Animations**: hover lift, entrance fade-in-up
- **Features**: optional header, footer, image support

#### Input Component
- **Types**: text, email, textarea, select
- **States**: default, focus, error, disabled
- **Features**: label, helper text, validation display

### 2. Layout Components (`components/layout/`)

#### Header Component
- **Features**: 
  - Translucent background with backdrop blur
  - Scroll-triggered hide/show animation
  - Responsive design with mobile menu toggle
  - Smooth scroll navigation links

#### Navigation Component
- **Desktop**: Horizontal navigation with hover effects
- **Mobile**: Slide-in drawer with stagger animations
- **Features**: Active state indicators, smooth transitions

#### Footer Component
- **Content**: Social links, copyright, contact info
- **Animation**: Fade-in on scroll into view

### 3. Section Components (`components/sections/`)

#### Hero Section
- **Layout**: Full viewport height with centered content
- **Animations**: 
  - Typewriter effect for main heading
  - Staggered fade-in for subtext and CTA
  - Subtle parallax background
- **Features**: Scroll indicator, animated CTA button

#### About Section
- **Layout**: Two-column (image + content) on desktop
- **Content**: 
  - Personal introduction
  - Cal Poly education highlight
  - Skills overview with animated progress bars
- **Animations**: Scroll-triggered reveals, image parallax

#### Projects Section
- **Layout**: Grid of project cards
- **Features**:
  - Project filtering/categorization
  - Detailed modal views
  - Live demo and GitHub links
  - Tech stack badges
- **Animations**: Hover effects, staggered entrance

#### Skills Section
- **Layout**: Categorized skill groups
- **Features**:
  - Proficiency level indicators
  - Interactive skill cards
  - Technology logos/icons
- **Animations**: Progress bars, hover effects

#### Experience Section
- **Layout**: Timeline format
- **Content**:
  - Education (Cal Poly)
  - Work experience
  - Key achievements
- **Animations**: Timeline progression, content reveals

#### Contact Section
- **Features**:
  - Contact form with validation
  - Social media links
  - Email integration
- **Animations**: Form field focus effects, success states

### 4. Animation Components (`components/animations/`)

#### ScrollReveal Component
- **Purpose**: Wrapper for scroll-triggered animations
- **Features**: Intersection Observer integration
- **Props**: animation type, threshold, delay

#### ParallaxSection Component
- **Purpose**: Creates parallax scrolling effects
- **Features**: Configurable speed and direction
- **Performance**: Uses transform3d for GPU acceleration

#### TypewriterText Component
- **Purpose**: Animated text typing effect
- **Features**: Configurable speed, cursor blink
- **Use Cases**: Hero headings, emphasis text

#### CounterAnimation Component
- **Purpose**: Animated number counting
- **Features**: Configurable duration, easing
- **Use Cases**: Statistics, achievements

## Animation Strategy

### Entrance Animations
1. **Fade In Up**: Default for most content sections
2. **Stagger**: For lists and grouped elements
3. **Scale In**: For cards and interactive elements

### Scroll Animations
1. **Reveal**: Elements animate in when 20% visible
2. **Parallax**: Background elements move at different speeds
3. **Progress**: Bars and counters animate based on scroll position

### Interaction Animations
1. **Hover Effects**: Lift, scale, color transitions
2. **Button States**: Press effects, loading states
3. **Form Interactions**: Focus states, validation feedback

### Page Transitions
1. **Route Changes**: Smooth fade transitions
2. **Section Navigation**: Smooth scroll with offset
3. **Modal/Overlay**: Backdrop blur with scale animation

## Performance Considerations

### Animation Optimization
- Use `transform` and `opacity` for animations
- Implement `will-change` for complex animations
- Respect `prefers-reduced-motion` settings
- Use `IntersectionObserver` for scroll animations

### Code Splitting
- Lazy load non-critical components
- Split animations by route
- Progressive enhancement approach

### Image Optimization
- Use Next.js Image component
- Implement lazy loading
- Provide appropriate sizes and formats

## Responsive Design Strategy

### Breakpoints
- **Mobile**: 0-767px
- **Tablet**: 768-1023px
- **Desktop**: 1024-1279px
- **Large**: 1280px+

### Component Adaptations
- **Navigation**: Hamburger menu on mobile
- **Grid Layouts**: Stack on mobile, grid on desktop
- **Typography**: Responsive font scaling
- **Spacing**: Reduced padding/margins on mobile

## Accessibility Features

### Keyboard Navigation
- Focus management for interactive elements
- Skip links for main content
- Proper tab order

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Alt text for images

### Motion Accessibility
- Respect reduced motion preferences
- Provide motion toggle control
- Ensure content is accessible without animations

This architecture ensures scalable, maintainable, and performant code while delivering the standout animations and smooth interactions you're looking for.
