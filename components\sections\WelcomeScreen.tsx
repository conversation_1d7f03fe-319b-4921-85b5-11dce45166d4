'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui';
import { TypewriterText } from '@/components/animations/TypewriterText';
import { Aurora } from '@/components/ui/Aurora';
import { PERSONAL_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onEnter: () => void;
  className?: string;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onEnter,
  className = '',
}) => {
  const [showContent, setShowContent] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if mobile
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Stagger the appearance of content
    const timer1 = setTimeout(() => setShowContent(true), 500);
    const timer2 = setTimeout(() => setShowButton(true), 1200);

    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);



  return (
    <div className={cn(
      'fixed inset-0 z-50',
      'flex items-center justify-center overflow-hidden',
      className
    )}>
      {/* Aurora Background */}
      <Aurora className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-black/50" />
      </Aurora>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Name Display */}
        <AnimatePresence>
          {showContent && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="mb-8"
            >
              <h1 className="text-4xl sm:text-6xl md:text-8xl lg:text-9xl font-bold text-white mb-4 relative">
                <motion.span
                  className="block bg-gradient-to-r from-white via-purple-200 to-cyan-200 bg-clip-text text-transparent relative"
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                  style={{
                    backgroundSize: '200% 200%',
                    textShadow: '0 0 30px rgba(147, 51, 234, 0.3), 0 0 60px rgba(59, 130, 246, 0.2)',
                  }}
                >
                  {PERSONAL_INFO.name.split(' ')[0]}
                </motion.span>
                <motion.span
                  className="block bg-gradient-to-r from-purple-300 via-pink-200 to-blue-200 bg-clip-text text-transparent relative"
                  animate={{
                    backgroundPosition: ['100% 50%', '0% 50%', '100% 50%'],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: 1,
                  }}
                  style={{
                    backgroundSize: '200% 200%',
                    textShadow: '0 0 30px rgba(236, 72, 153, 0.3), 0 0 60px rgba(147, 51, 234, 0.2)',
                  }}
                >
                  {PERSONAL_INFO.name.split(' ')[1]}
                </motion.span>

                {/* Name Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 blur-3xl -z-10 animate-pulse" />
              </h1>
            </motion.div>
          )}
        </AnimatePresence>



        {/* Enter Button */}
        <AnimatePresence>
          {showButton && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, ease: 'easeOut' }}
              className="relative"
            >
              <Button
                onClick={onEnter}
                size="lg"
                aria-label="Enter the portfolio website"
                className={cn(
                  'group relative overflow-hidden',
                  'bg-gradient-to-r from-purple-600 to-blue-600',
                  'hover:from-purple-500 hover:to-blue-500',
                  'text-white font-semibold px-8 py-4',
                  'border-0 shadow-2xl shadow-purple-500/25',
                  'transition-all duration-300',
                  'hover:shadow-purple-500/40 hover:scale-105',
                  'focus:outline-none focus:ring-4 focus:ring-purple-500/50'
                )}
              >
                <span className="relative z-10 flex items-center gap-3">
                  <Sparkles className="w-5 h-5" />
                  Enter Website
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
                
                {/* Button glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
              </Button>

              {/* Simple floating sparkles around button */}
              <div className="absolute inset-0 pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={`sparkle-${i}`}
                    className="absolute w-1 h-1 bg-white rounded-full"
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + (i % 2) * 40}%`,
                      boxShadow: '0 0 4px rgba(255, 255, 255, 0.8)',
                    }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      y: [-10, -20, -10],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: 'easeInOut',
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>


    </div>
  );
};
