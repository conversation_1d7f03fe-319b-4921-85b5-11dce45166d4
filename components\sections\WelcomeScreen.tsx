'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, Zap } from 'lucide-react';
import { Button } from '@/components/ui';
import { Aurora } from '@/components/ui/Aurora';
import { PERSONAL_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onEnter: () => void;
  className?: string;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onEnter,
  className = '',
}) => {
  const [showContent, setShowContent] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [showSubtitle, setShowSubtitle] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if mobile
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Enhanced stagger timing for better visual flow
    const timer1 = setTimeout(() => setShowContent(true), 800);
    const timer2 = setTimeout(() => setShowSubtitle(true), 1600);
    const timer3 = setTimeout(() => setShowButton(true), 2200);

    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);



  return (
    <div className={cn(
      'fixed inset-0 z-50 overflow-hidden',
      className
    )}>
      {/* Aurora Background */}
      <Aurora className="absolute inset-0" />

      {/* Enhanced gradient overlay for better integration */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-transparent to-black/30 z-5" />

      {/* Cosmic particle field */}
      <div className="absolute inset-0 z-5">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={`cosmic-particle-${i}`}
            className="absolute w-0.5 h-0.5 bg-white/40 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      {/* Main Content - Absolutely Centered */}
      <div className="absolute inset-0 z-10 flex items-center justify-center">
        <div className="text-center px-6 max-w-5xl mx-auto relative">
          {/* Apple-style Glass Effect Name Display */}
          <AnimatePresence>
            {showContent && (
              <motion.div
                initial={{ opacity: 0, y: 80, scale: 0.8 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  scale: 1,
                }}
                transition={{
                  duration: 1.2,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "spring",
                  stiffness: 100
                }}
                className="mb-12 relative"
              >
                <h1 className="text-4xl sm:text-6xl md:text-8xl lg:text-9xl font-bold relative">
                  {/* First Name with Glass Effect */}
                  <motion.span
                    className="block relative"
                    animate={{
                      y: [0, -8, 0],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <span
                      className="text-white/90 relative z-10 backdrop-blur-sm"
                      style={{
                        textShadow: '0 0 1px rgba(255, 255, 255, 0.5), 0 1px 2px rgba(0, 0, 0, 0.8)',
                        WebkitTextStroke: '1px rgba(255, 255, 255, 0.1)',
                      }}
                    >
                      {PERSONAL_INFO.name.split(' ')[0]}
                    </span>
                  </motion.span>

                  {/* Last Name with Glass Effect */}
                  <motion.span
                    className="block relative"
                    animate={{
                      y: [0, 8, 0],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 3,
                    }}
                  >
                    <span
                      className="text-white/90 relative z-10 backdrop-blur-sm"
                      style={{
                        textShadow: '0 0 1px rgba(255, 255, 255, 0.5), 0 1px 2px rgba(0, 0, 0, 0.8)',
                        WebkitTextStroke: '1px rgba(255, 255, 255, 0.1)',
                      }}
                    >
                      {PERSONAL_INFO.name.split(' ')[1]}
                    </span>
                  </motion.span>
                </h1>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Cosmic Subtitle */}
          <AnimatePresence>
            {showSubtitle && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
                className="mb-16"
              >
                <motion.p
                  className="text-lg sm:text-xl text-cyan-100/80 font-light tracking-wider"
                  animate={{
                    opacity: [0.6, 1, 0.6],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <span className="inline-flex items-center gap-2">
                    <Zap className="w-4 h-4 text-cyan-400" />
                    Full Stack Developer & Digital Architect
                    <Zap className="w-4 h-4 text-purple-400" />
                  </span>
                </motion.p>
              </motion.div>
            )}
          </AnimatePresence>



          {/* Enhanced Enter Website Button */}
          <AnimatePresence>
            {showButton && (
              <motion.div
                initial={{ opacity: 0, scale: 0.5, y: 50 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  y: 0,
                }}
                transition={{
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "spring",
                  stiffness: 120
                }}
                className="relative"
              >


                <Button
                  onClick={onEnter}
                  size="lg"
                  aria-label="Enter the portfolio website"
                  className={cn(
                    'group relative overflow-hidden',
                    'bg-white/10 hover:bg-white/20',
                    'text-white font-semibold px-10 py-5 text-lg',
                    'border border-white/20 hover:border-white/30 rounded-xl',
                    'backdrop-blur-md',
                    'shadow-2xl shadow-black/20',
                    'transition-all duration-300',
                    'hover:scale-105 hover:shadow-black/30',
                    'focus:outline-none focus:ring-4 focus:ring-white/30'
                  )}
                >
                  {/* Subtle shimmer effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                    animate={{
                      x: ['-100%', '100%'],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />

                  <span className="relative z-10 flex items-center gap-4">
                    <Sparkles className="w-6 h-6 text-white/80" />
                    <span className="tracking-wide">Enter Website</span>
                    <ArrowRight className="w-6 h-6 text-white/80 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                </Button>


              </motion.div>
            )}
            </AnimatePresence>
        </div>
      </div>
    </div>
  );
};
