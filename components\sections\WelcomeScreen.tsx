'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui';
import { TypewriterText } from '@/components/animations/TypewriterText';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import { PERSONAL_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onEnter: () => void;
  className?: string;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onEnter,
  className = '',
}) => {
  const [showContent, setShowContent] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if mobile
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Stagger the appearance of content
    const timer1 = setTimeout(() => setShowContent(true), 500);
    const timer2 = setTimeout(() => setShowButton(true), 2000);

    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  const roles = [
    'Full-Stack Developer',
    'Problem Solver',
    'Creative Thinker',
    'Code Architect',
    'Digital Craftsman'
  ];

  return (
    <div className={cn(
      'fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900',
      'flex items-center justify-center overflow-hidden',
      className
    )}>
      {/* Particle Background */}
      <ParticleSystem
        particleCount={isMobile ? 25 : 50}
        className="opacity-30"
        color="rgba(147, 51, 234, 0.3)"
        speed={0.3}
      />

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Name Display */}
        <AnimatePresence>
          {showContent && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="mb-8"
            >
              <h1 className="text-4xl sm:text-6xl md:text-8xl lg:text-9xl font-bold text-white mb-4">
                <span className="bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-transparent">
                  {PERSONAL_INFO.name.split(' ')[0]}
                </span>
                <br />
                <span className="bg-gradient-to-r from-purple-300 via-white to-purple-300 bg-clip-text text-transparent">
                  {PERSONAL_INFO.name.split(' ')[1]}
                </span>
              </h1>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Role Definitions */}
        <AnimatePresence>
          {showContent && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: 'easeOut' }}
              className="mb-12"
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 max-w-3xl mx-auto">
                {roles.map((role, index) => (
                  <motion.div
                    key={role}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ 
                      duration: 0.5, 
                      delay: 0.5 + index * 0.1,
                      ease: 'easeOut'
                    }}
                    className="relative group"
                  >
                    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg p-3 sm:p-4 hover:bg-white/10 transition-all duration-300">
                      <p className="text-white/90 text-xs sm:text-sm md:text-base font-medium">
                        {role}
                      </p>
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Subtitle */}
        <AnimatePresence>
          {showContent && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2, ease: 'easeOut' }}
              className="mb-12"
            >
              <p className="text-white/70 text-lg md:text-xl max-w-2xl mx-auto leading-relaxed">
                {PERSONAL_INFO.subtitle}
              </p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enter Button */}
        <AnimatePresence>
          {showButton && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, ease: 'easeOut' }}
              className="relative"
            >
              <Button
                onClick={onEnter}
                size="lg"
                aria-label="Enter the portfolio website"
                className={cn(
                  'group relative overflow-hidden',
                  'bg-gradient-to-r from-purple-600 to-blue-600',
                  'hover:from-purple-500 hover:to-blue-500',
                  'text-white font-semibold px-8 py-4',
                  'border-0 shadow-2xl shadow-purple-500/25',
                  'transition-all duration-300',
                  'hover:shadow-purple-500/40 hover:scale-105',
                  'focus:outline-none focus:ring-4 focus:ring-purple-500/50'
                )}
              >
                <span className="relative z-10 flex items-center gap-3">
                  <Sparkles className="w-5 h-5" />
                  Enter Website
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
                
                {/* Button glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
              </Button>

              {/* Floating sparkles around button */}
              <div className="absolute inset-0 pointer-events-none">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-white rounded-full"
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + (i % 2) * 40}%`,
                    }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                      y: [-10, -20, -10],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: 'easeInOut',
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Ambient glow effects */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl" />
    </div>
  );
};
