'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>R<PERSON>, <PERSON>rk<PERSON>, Zap } from 'lucide-react';
import { Button } from '@/components/ui';
import { Aurora } from '@/components/ui/Aurora';
import { PERSONAL_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onEnter: () => void;
  className?: string;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onEnter,
  className = '',
}) => {
  const [showContent, setShowContent] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [showSubtitle, setShowSubtitle] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if mobile
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Enhanced stagger timing for better visual flow
    const timer1 = setTimeout(() => setShowContent(true), 800);
    const timer2 = setTimeout(() => setShowSubtitle(true), 1600);
    const timer3 = setTimeout(() => setShowButton(true), 2200);

    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);



  return (
    <div className={cn(
      'fixed inset-0 z-50 overflow-hidden',
      className
    )}>
      {/* Aurora Background */}
      <Aurora className="absolute inset-0" />

      {/* Enhanced gradient overlay for better integration */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/10 via-transparent to-black/30 z-5" />

      {/* Cosmic particle field */}
      <div className="absolute inset-0 z-5">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={`cosmic-particle-${i}`}
            className="absolute w-0.5 h-0.5 bg-white/40 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.2, 0.8, 0.2],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 2,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      {/* Main Content - Absolutely Centered */}
      <div className="absolute inset-0 z-10 flex items-center justify-center">
        <div className="text-center px-6 max-w-5xl mx-auto relative">
          {/* Enhanced Name Display with Cosmic Effects */}
          <AnimatePresence>
            {showContent && (
              <motion.div
                initial={{ opacity: 0, y: 80, scale: 0.8 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  scale: 1,
                }}
                transition={{
                  duration: 1.2,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "spring",
                  stiffness: 100
                }}
                className="mb-12 relative"
              >
                {/* Cosmic energy rings around name */}
                <motion.div
                  className="absolute inset-0 -m-8"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <div className="absolute inset-0 rounded-full border border-cyan-400/20 blur-sm" />
                  <div className="absolute inset-4 rounded-full border border-purple-400/15 blur-sm" />
                </motion.div>

                <h1 className="text-4xl sm:text-6xl md:text-8xl lg:text-9xl font-bold relative">
                  {/* First Name with Enhanced Cosmic Glow */}
                  <motion.span
                    className="block relative"
                    animate={{
                      y: [0, -8, 0],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <span
                      className="bg-gradient-to-r from-cyan-200 via-white to-purple-200 bg-clip-text text-transparent relative z-10"
                      style={{
                        filter: 'drop-shadow(0 0 20px rgba(34, 211, 238, 0.4)) drop-shadow(0 0 40px rgba(147, 51, 234, 0.3))',
                      }}
                    >
                      {PERSONAL_INFO.name.split(' ')[0]}
                    </span>
                    {/* Multi-layered glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/30 via-white/20 to-purple-400/30 blur-xl -z-10" />
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-300/20 via-white/10 to-purple-300/20 blur-2xl -z-20" />
                  </motion.span>

                  {/* Last Name with Complementary Cosmic Glow */}
                  <motion.span
                    className="block relative"
                    animate={{
                      y: [0, 8, 0],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: 3,
                    }}
                  >
                    <span
                      className="bg-gradient-to-r from-purple-200 via-pink-100 to-cyan-200 bg-clip-text text-transparent relative z-10"
                      style={{
                        filter: 'drop-shadow(0 0 20px rgba(147, 51, 234, 0.4)) drop-shadow(0 0 40px rgba(236, 72, 153, 0.3))',
                      }}
                    >
                      {PERSONAL_INFO.name.split(' ')[1]}
                    </span>
                    {/* Multi-layered glow effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-400/30 via-pink-400/20 to-cyan-400/30 blur-xl -z-10" />
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-cyan-300/20 blur-2xl -z-20" />
                  </motion.span>

                  {/* Enhanced ambient glow */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-purple-500/15 to-pink-500/10 blur-3xl -z-30"
                    animate={{
                      opacity: [0.3, 0.7, 0.3],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                </h1>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Cosmic Subtitle */}
          <AnimatePresence>
            {showSubtitle && (
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: 'easeOut' }}
                className="mb-16"
              >
                <motion.p
                  className="text-lg sm:text-xl text-cyan-100/80 font-light tracking-wider"
                  animate={{
                    opacity: [0.6, 1, 0.6],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                >
                  <span className="inline-flex items-center gap-2">
                    <Zap className="w-4 h-4 text-cyan-400" />
                    Full Stack Developer & Digital Architect
                    <Zap className="w-4 h-4 text-purple-400" />
                  </span>
                </motion.p>
              </motion.div>
            )}
          </AnimatePresence>



          {/* Enhanced Enter Website Button */}
          <AnimatePresence>
            {showButton && (
              <motion.div
                initial={{ opacity: 0, scale: 0.5, y: 50 }}
                animate={{
                  opacity: 1,
                  scale: 1,
                  y: 0,
                }}
                transition={{
                  duration: 0.8,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "spring",
                  stiffness: 120
                }}
                className="relative"
              >
                {/* Cosmic energy field around button */}
                <motion.div
                  className="absolute -inset-8 rounded-full"
                  animate={{
                    background: [
                      'radial-gradient(circle, rgba(34, 211, 238, 0.1) 0%, transparent 70%)',
                      'radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%)',
                      'radial-gradient(circle, rgba(236, 72, 153, 0.1) 0%, transparent 70%)',
                      'radial-gradient(circle, rgba(34, 211, 238, 0.1) 0%, transparent 70%)',
                    ],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />

                <Button
                  onClick={onEnter}
                  size="lg"
                  aria-label="Enter the portfolio website"
                  className={cn(
                    'group relative overflow-hidden',
                    'bg-gradient-to-r from-cyan-600/90 via-purple-600/90 to-pink-600/90',
                    'hover:from-cyan-500 hover:via-purple-500 hover:to-pink-500',
                    'text-white font-semibold px-10 py-5 text-lg',
                    'border border-cyan-400/30 rounded-xl',
                    'shadow-2xl shadow-cyan-500/20',
                    'transition-all duration-500',
                    'hover:shadow-cyan-400/40 hover:shadow-2xl hover:scale-110',
                    'hover:border-cyan-300/50',
                    'focus:outline-none focus:ring-4 focus:ring-cyan-400/50',
                    'backdrop-blur-sm'
                  )}
                >
                  {/* Animated background shimmer */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                    animate={{
                      x: ['-100%', '100%'],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />

                  <span className="relative z-10 flex items-center gap-4">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                    >
                      <Sparkles className="w-6 h-6 text-cyan-300" />
                    </motion.div>

                    <span className="tracking-wide">Enter Website</span>

                    <motion.div
                      className="group-hover:translate-x-2 transition-transform duration-300"
                      animate={{
                        x: [0, 4, 0],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    >
                      <ArrowRight className="w-6 h-6 text-purple-300" />
                    </motion.div>
                  </span>

                  {/* Enhanced button glow effects */}
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 via-purple-400/20 to-pink-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl" />
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-300/10 via-purple-300/10 to-pink-300/10 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                </Button>

                {/* Enhanced cosmic particles around button */}
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(12)].map((_, i) => (
                    <motion.div
                      key={`cosmic-sparkle-${i}`}
                      className="absolute w-1.5 h-1.5 rounded-full"
                      style={{
                        left: `${15 + (i * 8) % 70}%`,
                        top: `${20 + (i % 3) * 25}%`,
                        background: i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#a855f7' : '#ec4899',
                        boxShadow: `0 0 8px ${i % 3 === 0 ? '#22d3ee' : i % 3 === 1 ? '#a855f7' : '#ec4899'}`,
                      }}
                      animate={{
                        opacity: [0, 1, 0],
                        scale: [0, 1.5, 0],
                        y: [-15, -30, -15],
                        rotate: [0, 180, 360],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: 'easeInOut',
                      }}
                    />
                  ))}
                </div>

                {/* Orbital rings */}
                <motion.div
                  className="absolute inset-0 pointer-events-none"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                >
                  <div className="absolute inset-0 border border-cyan-400/20 rounded-full scale-150" />
                </motion.div>
                <motion.div
                  className="absolute inset-0 pointer-events-none"
                  animate={{ rotate: -360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <div className="absolute inset-0 border border-purple-400/15 rounded-full scale-125" />
                </motion.div>
              </motion.div>
            )}
            </AnimatePresence>
        </div>
      </div>
    </div>
  );
};
