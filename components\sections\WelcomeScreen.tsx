'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui';
import { TypewriterText } from '@/components/animations/TypewriterText';
import { ParticleSystem } from '@/components/animations/ParticleSystem';
import { CosmicTrails } from '@/components/animations/CosmicTrails';
import { CursorTrail } from '@/components/animations/CursorTrail';
import { PERSONAL_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface WelcomeScreenProps {
  onEnter: () => void;
  className?: string;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  onEnter,
  className = '',
}) => {
  const [showContent, setShowContent] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if mobile
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // Stagger the appearance of content
    const timer1 = setTimeout(() => setShowContent(true), 500);
    const timer2 = setTimeout(() => setShowButton(true), 1200);

    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);



  return (
    <div className={cn(
      'fixed inset-0 z-50 bg-gradient-to-br from-black via-gray-900/50 to-black',
      'flex items-center justify-center overflow-hidden',
      className
    )}>
      {/* Enhanced Background Effects */}
      {/* Interactive Cursor Trail */}
      <CursorTrail
        className="opacity-80"
        particleLifespan={2000}
        maxParticles={60}
      />

      {/* Cosmic Trails */}
      <CosmicTrails
        className="opacity-60"
        trailCount={isMobile ? 4 : 8}
      />

      {/* Primary Particle System */}
      <ParticleSystem
        particleCount={isMobile ? 40 : 80}
        className="opacity-40"
        color="rgba(147, 51, 234, 0.4)"
        speed={0.2}
      />

      {/* Secondary Particle Layer */}
      <div className="absolute inset-0 overflow-hidden">
        <ParticleSystem
          particleCount={isMobile ? 20 : 40}
          className="opacity-25"
          color="rgba(59, 130, 246, 0.3)"
          speed={0.1}
        />
      </div>

      {/* Floating Cosmic Orbs */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`orb-${i}`}
            className="absolute rounded-full blur-xl"
            style={{
              width: `${60 + i * 20}px`,
              height: `${60 + i * 20}px`,
              left: `${10 + i * 12}%`,
              top: `${15 + (i % 3) * 25}%`,
              background: i % 3 === 0
                ? 'radial-gradient(circle, rgba(147, 51, 234, 0.4) 0%, rgba(147, 51, 234, 0.15) 50%, transparent 100%)'
                : i % 3 === 1
                ? 'radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, rgba(59, 130, 246, 0.15) 50%, transparent 100%)'
                : 'radial-gradient(circle, rgba(236, 72, 153, 0.4) 0%, rgba(236, 72, 153, 0.15) 50%, transparent 100%)'
            }}
            animate={{
              x: [0, 30, -20, 0],
              y: [0, -25, 15, 0],
              scale: [1, 1.2, 0.8, 1],
              opacity: [0.3, 0.6, 0.2, 0.3],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 1.2,
            }}
          />
        ))}
      </div>

      {/* Constellation Patterns */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={`star-${i}`}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              boxShadow: '0 0 8px rgba(255, 255, 255, 0.9), 0 0 16px rgba(147, 51, 234, 0.6), 0 0 24px rgba(59, 130, 246, 0.3)',
            }}
            animate={{
              opacity: [0.2, 1, 0.2],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 3 + Math.random() * 4,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      {/* Enhanced Gradient Overlays for Deep Space */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-900/30 via-transparent to-blue-900/25" />
        <div className="absolute top-0 right-0 w-full h-full bg-gradient-to-bl from-pink-900/20 via-transparent to-purple-900/25" />
        <div className="absolute bottom-0 left-0 w-full h-full bg-gradient-to-tr from-blue-900/25 via-transparent to-purple-900/30" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20" />
      </div>

      {/* Animated Cosmic Rays */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={`ray-${i}`}
            className="absolute bg-gradient-to-r from-transparent via-white/15 to-transparent"
            style={{
              width: '2px',
              height: '100px',
              left: `${20 + i * 15}%`,
              top: `${10 + i * 10}%`,
              transform: `rotate(${45 + i * 30}deg)`,
            }}
            animate={{
              opacity: [0, 0.6, 0],
              scaleY: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.5,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-4xl mx-auto">
        {/* Name Display */}
        <AnimatePresence>
          {showContent && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="mb-8"
            >
              <h1 className="text-4xl sm:text-6xl md:text-8xl lg:text-9xl font-bold text-white mb-4 relative">
                <motion.span
                  className="block bg-gradient-to-r from-white via-purple-200 to-cyan-200 bg-clip-text text-transparent relative"
                  animate={{
                    backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: 'easeInOut',
                  }}
                  style={{
                    backgroundSize: '200% 200%',
                    textShadow: '0 0 30px rgba(147, 51, 234, 0.3), 0 0 60px rgba(59, 130, 246, 0.2)',
                  }}
                >
                  {PERSONAL_INFO.name.split(' ')[0]}
                </motion.span>
                <motion.span
                  className="block bg-gradient-to-r from-purple-300 via-pink-200 to-blue-200 bg-clip-text text-transparent relative"
                  animate={{
                    backgroundPosition: ['100% 50%', '0% 50%', '100% 50%'],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    delay: 1,
                  }}
                  style={{
                    backgroundSize: '200% 200%',
                    textShadow: '0 0 30px rgba(236, 72, 153, 0.3), 0 0 60px rgba(147, 51, 234, 0.2)',
                  }}
                >
                  {PERSONAL_INFO.name.split(' ')[1]}
                </motion.span>

                {/* Name Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 blur-3xl -z-10 animate-pulse" />
              </h1>
            </motion.div>
          )}
        </AnimatePresence>



        {/* Enter Button */}
        <AnimatePresence>
          {showButton && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, ease: 'easeOut' }}
              className="relative"
            >
              <Button
                onClick={onEnter}
                size="lg"
                aria-label="Enter the portfolio website"
                className={cn(
                  'group relative overflow-hidden',
                  'bg-gradient-to-r from-purple-600 to-blue-600',
                  'hover:from-purple-500 hover:to-blue-500',
                  'text-white font-semibold px-8 py-4',
                  'border-0 shadow-2xl shadow-purple-500/25',
                  'transition-all duration-300',
                  'hover:shadow-purple-500/40 hover:scale-105',
                  'focus:outline-none focus:ring-4 focus:ring-purple-500/50'
                )}
              >
                <span className="relative z-10 flex items-center gap-3">
                  <Sparkles className="w-5 h-5" />
                  Enter Website
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
                
                {/* Button glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300" />
              </Button>

              {/* Enhanced floating effects around button */}
              <div className="absolute inset-0 pointer-events-none">
                {/* Sparkles */}
                {[...Array(12)].map((_, i) => (
                  <motion.div
                    key={`sparkle-${i}`}
                    className="absolute w-1 h-1 bg-white rounded-full"
                    style={{
                      left: `${15 + i * 8}%`,
                      top: `${20 + (i % 3) * 20}%`,
                      boxShadow: '0 0 4px rgba(255, 255, 255, 0.8), 0 0 8px rgba(147, 51, 234, 0.6)',
                    }}
                    animate={{
                      opacity: [0, 1, 0],
                      scale: [0, 1.5, 0],
                      y: [-15, -30, -15],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: 'easeInOut',
                    }}
                  />
                ))}

                {/* Orbiting particles */}
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={`orbit-${i}`}
                    className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-sm"
                    style={{
                      left: '50%',
                      top: '50%',
                    }}
                    animate={{
                      x: [0, Math.cos((i * 45) * Math.PI / 180) * 60],
                      y: [0, Math.sin((i * 45) * Math.PI / 180) * 60],
                      opacity: [0.3, 0.8, 0.3],
                      scale: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      delay: i * 0.5,
                      ease: 'easeInOut',
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Enhanced Ambient Glow Effects for Deep Space */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse" />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-purple-500/8 via-pink-500/8 to-transparent rounded-full blur-3xl" />

      {/* Additional Depth Layers */}
      <motion.div
        className="absolute top-1/3 right-1/3 w-72 h-72 bg-pink-500/15 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.15, 0.25, 0.15],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />

      <motion.div
        className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-cyan-500/12 rounded-full blur-2xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.12, 0.2, 0.12],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 2,
        }}
      />

      {/* Deep Space Vignette Effect */}
      <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-black/40" />
    </div>
  );
};
