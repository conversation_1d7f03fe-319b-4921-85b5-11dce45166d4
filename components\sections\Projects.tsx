'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, Github, Eye, Filter, X } from 'lucide-react';
import { Card, CardContent, Button, Modal } from '@/components/ui';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { PROJECTS } from '@/lib/constants';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';
import { Project } from '@/lib/types';

const projectCategories = [
  { id: 'all', label: 'All Projects', count: PROJECTS.length },
  { id: 'web', label: 'Web Apps', count: PROJECTS.filter(p => p.category === 'web').length },
  { id: 'game', label: 'Games', count: PROJECTS.filter(p => p.category === 'game').length },
];

export const Projects: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation({ threshold: 0.1 });
  const [activeCategory, setActiveCategory] = useState('all');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  const filteredProjects = activeCategory === 'all' 
    ? PROJECTS 
    : PROJECTS.filter(project => project.category === activeCategory);

  const featuredProjects = PROJECTS.filter(project => project.featured);

  return (
    <section 
      id="projects" 
      ref={ref}
      className="py-20 lg:py-32 bg-gradient-to-b from-background to-muted/30"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          animate={isVisible ? "animate" : "initial"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={staggerItem} className="text-center mb-16">
            <motion.h2
              className="text-3xl md:text-5xl font-bold text-foreground mb-6"
              variants={fadeInUp}
            >
              Featured Projects
            </motion.h2>
            <motion.p
              className="text-lg text-muted-foreground max-w-3xl mx-auto mb-8"
              variants={fadeInUp}
            >
              A showcase of my work including modern web applications, interactive dashboards,
              and creative experiments that demonstrate my technical skills and design sensibility.
            </motion.p>

            {/* Project Stats */}
            <motion.div
              variants={fadeInUp}
              className="flex justify-center gap-8 text-center"
            >
              <div>
                <div className="text-2xl font-bold text-primary">{PROJECTS.length}</div>
                <div className="text-sm text-muted-foreground">Total Projects</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">{featuredProjects.length}</div>
                <div className="text-sm text-muted-foreground">Featured</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {new Set(PROJECTS.flatMap(p => p.techStack.map(t => t.name))).size}
                </div>
                <div className="text-sm text-muted-foreground">Technologies</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Category Filter */}
          <motion.div variants={staggerItem} className="flex justify-center mb-12">
            <div className="flex flex-wrap gap-2 p-2 bg-muted/50 rounded-lg">
              {projectCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? 'bg-primary text-primary-foreground shadow-md'
                      : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                  }`}
                >
                  {category.label}
                  <span className="ml-2 text-xs opacity-70">({category.count})</span>
                </button>
              ))}
            </div>
          </motion.div>

          {/* Featured Projects Grid */}
          <motion.div variants={staggerItem} className="mb-16">
            <h3 className="text-2xl font-semibold text-foreground mb-8 text-center">
              Highlighted Work
            </h3>
            <div className="grid md:grid-cols-2 gap-8">
              {featuredProjects.map((project, index) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  index={index}
                  onViewDetails={() => setSelectedProject(project)}
                  featured
                />
              ))}
            </div>
          </motion.div>

          {/* All Projects Grid */}
          <motion.div variants={staggerItem}>
            <h3 className="text-2xl font-semibold text-foreground mb-8 text-center">
              All Projects
            </h3>
            <AnimatePresence mode="wait">
              <motion.div
                key={activeCategory}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="grid md:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {filteredProjects.map((project, index) => (
                  <ProjectCard
                    key={project.id}
                    project={project}
                    index={index}
                    onViewDetails={() => setSelectedProject(project)}
                  />
                ))}
              </motion.div>
            </AnimatePresence>
          </motion.div>
        </motion.div>
      </div>

      {/* Project Detail Modal */}
      <ProjectModal
        project={selectedProject}
        isOpen={!!selectedProject}
        onClose={() => setSelectedProject(null)}
      />
    </section>
  );
};

interface ProjectCardProps {
  project: Project;
  index: number;
  onViewDetails: () => void;
  featured?: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ 
  project, 
  index, 
  onViewDetails, 
  featured = false 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      whileHover={{ y: -5 }}
      className="group"
    >
      <Card className={`overflow-hidden h-full hover:shadow-xl transition-all duration-300 ${
        featured ? 'border-primary/20' : ''
      }`}>
        <div className="relative overflow-hidden">
          <div className="aspect-video bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center relative">
            {/* Project-specific visual representation */}
            {project.id === 'revision-official' && (
              <div className="text-center">
                <div className="text-4xl mb-2">🛍️</div>
                <div className="text-sm font-medium text-primary/60">E-commerce Platform</div>
              </div>
            )}
            {project.id === 'beincourt-dashboard' && (
              <div className="text-center">
                <div className="text-4xl mb-2">📊</div>
                <div className="text-sm font-medium text-primary/60">Analytics Dashboard</div>
              </div>
            )}
            {project.id === 'web-game-collection' && (
              <div className="text-center">
                <div className="text-4xl mb-2">🎮</div>
                <div className="text-sm font-medium text-primary/60">Interactive Games</div>
              </div>
            )}
            {project.id === 'portfolio-experiments' && (
              <div className="text-center">
                <div className="text-4xl mb-2">🎨</div>
                <div className="text-sm font-medium text-primary/60">Design Experiments</div>
              </div>
            )}
            {!['revision-official', 'beincourt-dashboard', 'web-game-collection', 'portfolio-experiments'].includes(project.id) && (
              <div className="text-6xl opacity-20">🚀</div>
            )}
          </div>
          <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center gap-3">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              whileHover={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="secondary"
                size="sm"
                onClick={onViewDetails}
                className="bg-white/90 text-black hover:bg-white transform hover:scale-105 transition-transform"
              >
                <Eye size={16} className="mr-2" />
                View Details
              </Button>
            </motion.div>
            {project.links.find(link => link.type === 'live') && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                whileHover={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
              >
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => window.open(project.links.find(link => link.type === 'live')?.url, '_blank')}
                  className="bg-white/90 text-black hover:bg-white transform hover:scale-105 transition-transform"
                >
                  <ExternalLink size={16} className="mr-2" />
                  Live Demo
                </Button>
              </motion.div>
            )}
          </div>
          {featured && (
            <div className="absolute top-3 left-3 bg-primary text-primary-foreground px-2 py-1 rounded-md text-xs font-medium">
              Featured
            </div>
          )}
        </div>
        
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
              {project.title}
            </h3>
            <div className="flex items-center gap-2">
              <span className={`text-xs px-2 py-1 rounded-full ${
                project.status === 'completed'
                  ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                  : project.status === 'in-progress'
                  ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                  : 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
              }`}>
                {project.status === 'completed' ? '✓ Complete' :
                 project.status === 'in-progress' ? '⏳ In Progress' : '💡 Concept'}
              </span>
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                {project.year}
              </span>
            </div>
          </div>
          
          <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
            {project.description}
          </p>
          
          <div className="flex flex-wrap gap-2 mb-4">
            {project.techStack.slice(0, 3).map((tech) => (
              <span
                key={tech.name}
                className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md"
                style={{ 
                  backgroundColor: `${tech.color}15`,
                  color: tech.color 
                }}
              >
                {tech.name}
              </span>
            ))}
            {project.techStack.length > 3 && (
              <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                +{project.techStack.length - 3} more
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-3">
            {project.links.map((link) => (
              <a
                key={link.type}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                {link.type === 'github' && <Github size={16} />}
                {link.type === 'live' && <ExternalLink size={16} />}
              </a>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

interface ProjectModalProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
}

const ProjectModal: React.FC<ProjectModalProps> = ({ project, isOpen, onClose }) => {
  if (!project) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        <div className="flex items-start justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-foreground mb-2">{project.title}</h2>
            <p className="text-muted-foreground">{project.longDescription || project.description}</p>
          </div>
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="aspect-video bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg mb-6 flex items-center justify-center relative">
          {/* Project-specific visual representation for modal */}
          {project.id === 'revision-official' && (
            <div className="text-center">
              <div className="text-8xl mb-4">🛍️</div>
              <div className="text-lg font-medium text-primary/60">Modern E-commerce Platform</div>
            </div>
          )}
          {project.id === 'beincourt-dashboard' && (
            <div className="text-center">
              <div className="text-8xl mb-4">📊</div>
              <div className="text-lg font-medium text-primary/60">Business Intelligence Dashboard</div>
            </div>
          )}
          {project.id === 'web-game-collection' && (
            <div className="text-center">
              <div className="text-8xl mb-4">🎮</div>
              <div className="text-lg font-medium text-primary/60">Interactive Web Games</div>
            </div>
          )}
          {project.id === 'portfolio-experiments' && (
            <div className="text-center">
              <div className="text-8xl mb-4">🎨</div>
              <div className="text-lg font-medium text-primary/60">Creative Design Experiments</div>
            </div>
          )}
          {!['revision-official', 'beincourt-dashboard', 'web-game-collection', 'portfolio-experiments'].includes(project.id) && (
            <div className="text-8xl opacity-20">🚀</div>
          )}
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Technology Stack</h3>
            <div className="flex flex-wrap gap-2">
              {project.techStack.map((tech) => (
                <span
                  key={tech.name}
                  className="px-3 py-1 rounded-md text-sm font-medium"
                  style={{
                    backgroundColor: `${tech.color}15`,
                    color: tech.color
                  }}
                >
                  {tech.name}
                </span>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">Project Details</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Category:</span>
                <span className="capitalize">{project.category}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Year:</span>
                <span>{project.year}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Status:</span>
                <span className={`capitalize ${
                  project.status === 'completed' ? 'text-green-600' :
                  project.status === 'in-progress' ? 'text-yellow-600' : 'text-blue-600'
                }`}>
                  {project.status.replace('-', ' ')}
                </span>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">Project Links</h3>
            <div className="space-y-2">
              {project.links.map((link) => (
                <a
                  key={link.type}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors"
                >
                  {link.type === 'github' && <Github size={16} />}
                  {link.type === 'live' && <ExternalLink size={16} />}
                  {link.type === 'case-study' && <Eye size={16} />}
                  {link.label}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Projects;
