// Animation types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  ease?: number[];
  stagger?: number;
}

export interface ScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Button component types
export type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'link';
export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps extends BaseComponentProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  href?: string;
  type?: 'button' | 'submit' | 'reset';
}

// Card component types
export type CardVariant = 'default' | 'elevated' | 'bordered';

export interface CardProps extends BaseComponentProps {
  variant?: CardVariant;
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

// Input component types
export type InputType = 'text' | 'email' | 'password' | 'tel' | 'url';

export interface InputProps extends BaseComponentProps {
  type?: InputType;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
}

// Project data types
export interface TechStack {
  name: string;
  icon?: string;
  color?: string;
}

export interface ProjectLink {
  type: 'live' | 'github' | 'case-study';
  url: string;
  label: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  image: string;
  images?: string[];
  techStack: TechStack[];
  links: ProjectLink[];
  featured?: boolean;
  category: 'web' | 'mobile' | 'desktop' | 'game' | 'other';
  year: number;
  status: 'completed' | 'in-progress' | 'concept';
}

// Skill data types
export interface Skill {
  name: string;
  level: number; // 1-100
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'design' | 'other';
  icon?: string;
  color?: string;
  yearsOfExperience?: number;
}

export interface SkillCategory {
  name: string;
  skills: Skill[];
  icon?: string;
}

// Experience data types
export interface Experience {
  id: string;
  title: string;
  company: string;
  location: string;
  startDate: string;
  endDate?: string; // undefined means current
  description: string[];
  technologies: string[];
  type: 'work' | 'education' | 'project' | 'volunteer';
  logo?: string;
  website?: string;
}

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactFormErrors {
  name?: string;
  email?: string;
  subject?: string;
  message?: string;
}

// Social media types
export interface SocialLink {
  platform: string;
  url: string;
  icon: string;
  label: string;
}

// Navigation types
export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

export interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'light' | 'dark';
}

// Animation hook types
export interface UseScrollAnimationReturn {
  ref: React.RefObject<HTMLElement>;
  isVisible: boolean;
  hasAnimated: boolean;
}

export interface UseParallaxReturn {
  ref: React.RefObject<HTMLElement>;
  transform: string;
}

export interface UseIntersectionObserverOptions {
  threshold?: number | number[];
  rootMargin?: string;
  triggerOnce?: boolean;
}

export interface UseIntersectionObserverReturn {
  ref: React.RefObject<HTMLElement>;
  isIntersecting: boolean;
  entry?: IntersectionObserverEntry;
}

// Counter animation types
export interface UseCounterAnimationOptions {
  start?: number;
  end: number;
  duration?: number;
  delay?: number;
  easing?: (t: number) => number;
}

export interface UseCounterAnimationReturn {
  value: number;
  isAnimating: boolean;
  start: () => void;
  reset: () => void;
}

// Typewriter animation types
export interface UseTypewriterOptions {
  text: string;
  speed?: number;
  delay?: number;
  loop?: boolean;
  cursor?: boolean;
}

export interface UseTypewriterReturn {
  displayText: string;
  isComplete: boolean;
  isTyping: boolean;
}

// Scroll progress types
export interface UseScrollProgressReturn {
  scrollProgress: number;
  scrollDirection: 'up' | 'down';
  isScrolling: boolean;
}

// Device detection types
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

export interface UseDeviceDetectionReturn {
  deviceType: DeviceType;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

// Modal types
export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

// Toast notification types
export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface ToastContextType {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
}

// SEO types
export interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

// Analytics types
export interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
}

// Error boundary types
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T> {
  data?: T;
  loading: boolean;
  error?: string;
}

// API response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
}
