@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System Variables */
:root {
  /* Colors */
  --background: #ffffff;
  --foreground: #0a0a0a;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --border: #e5e5e5;
  --accent: #f5f5f5;
  --accent-foreground: #0a0a0a;
  --primary: #18181b;
  --secondary: #71717a;
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Animation Variables */
  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 700ms;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --border: #262626;
    --accent: #262626;
    --accent-foreground: #fafafa;
  }
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Smooth Scrolling Enhancement */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }

  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: var(--ease-out);
    transition-duration: var(--duration-fast);
  }
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Custom utility classes for navigation glow effects */
@layer utilities {
  .text-glow-white {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 16px rgba(255, 255, 255, 0.1);
  }

  .text-glow-white-subtle {
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.2), 0 0 12px rgba(255, 255, 255, 0.05);
  }

  .text-white-glow {
    color: white;
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.3), 0 0 12px rgba(255, 255, 255, 0.1);
  }

  .text-white-glow-subtle {
    color: white;
    text-shadow: 0 0 6px rgba(255, 255, 255, 0.2), 0 0 12px rgba(255, 255, 255, 0.05);
  }

  .text-gold-glow {
    color: #ffd700;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.4), 0 0 16px rgba(255, 215, 0, 0.2);
  }

  .section-transition-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 8rem;
    pointer-events: none;
  }

  .section-transition-top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rem;
    pointer-events: none;
  }

  .box-glow-white {
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.1), inset 0 0 16px rgba(255, 255, 255, 0.05);
  }

  .border-glow-white {
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.5), 0 0 8px rgba(255, 255, 255, 0.2);
  }

  .center-absolute {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .center-x {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  /* Gradient utilities */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }
}
