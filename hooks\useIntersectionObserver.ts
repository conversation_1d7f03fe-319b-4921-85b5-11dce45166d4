'use client';

import { useEffect, useRef, useState } from 'react';
import { UseIntersectionObserverOptions, UseIntersectionObserverReturn } from '@/lib/types';

/**
 * Custom hook for Intersection Observer API
 * Provides a clean interface for detecting when elements enter/exit the viewport
 */
export function useIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
): UseIntersectionObserverReturn {
  const {
    threshold = 0,
    rootMargin = '0px',
    triggerOnce = false,
  } = options;

  const ref = useRef<HTMLElement>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState<IntersectionObserverEntry>();

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([observerEntry]) => {
        setIsIntersecting(observerEntry.isIntersecting);
        setEntry(observerEntry);

        // If triggerOnce is true, disconnect after first intersection
        if (triggerOnce && observerEntry.isIntersecting) {
          observer.unobserve(element);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, rootMargin, triggerOnce]);

  return {
    ref,
    isIntersecting,
    entry,
  };
}

/**
 * Hook for observing multiple elements
 */
export function useMultipleIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
) {
  const {
    threshold = 0,
    rootMargin = '0px',
  } = options;

  const [entries, setEntries] = useState<Map<Element, IntersectionObserverEntry>>(new Map());
  const observerRef = useRef<IntersectionObserver>();

  const observe = (element: Element) => {
    if (!observerRef.current) {
      observerRef.current = new IntersectionObserver(
        (observerEntries) => {
          setEntries(prev => {
            const newEntries = new Map(prev);
            observerEntries.forEach(entry => {
              newEntries.set(entry.target, entry);
            });
            return newEntries;
          });
        },
        {
          threshold,
          rootMargin,
        }
      );
    }

    observerRef.current.observe(element);
  };

  const unobserve = (element: Element) => {
    if (observerRef.current) {
      observerRef.current.unobserve(element);
      setEntries(prev => {
        const newEntries = new Map(prev);
        newEntries.delete(element);
        return newEntries;
      });
    }
  };

  const disconnect = () => {
    if (observerRef.current) {
      observerRef.current.disconnect();
      setEntries(new Map());
    }
  };

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  return {
    entries,
    observe,
    unobserve,
    disconnect,
  };
}

/**
 * Hook for lazy loading images
 */
export function useLazyImage(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = useState(placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const { ref, isIntersecting } = useIntersectionObserver({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    if (isIntersecting && src) {
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
      };
      
      img.onerror = () => {
        setIsError(true);
      };
      
      img.src = src;
    }
  }, [isIntersecting, src]);

  return {
    ref,
    imageSrc,
    isLoaded,
    isError,
    isIntersecting,
  };
}

/**
 * Hook for detecting when element is near viewport
 */
export function useNearViewport(margin: string = '100px') {
  const { ref, isIntersecting } = useIntersectionObserver({
    rootMargin: margin,
    triggerOnce: true,
  });

  return {
    ref,
    isNearViewport: isIntersecting,
  };
}

/**
 * Hook for tracking scroll-based animations with percentage
 */
export function useScrollPercentage() {
  const ref = useRef<HTMLElement>(null);
  const [percentage, setPercentage] = useState(0);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const handleScroll = () => {
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Calculate how much of the element has been scrolled past
      const elementTop = rect.top;
      const elementHeight = rect.height;
      
      // Element is completely above viewport
      if (elementTop + elementHeight < 0) {
        setPercentage(100);
        return;
      }
      
      // Element is completely below viewport
      if (elementTop > windowHeight) {
        setPercentage(0);
        return;
      }
      
      // Element is partially or fully in viewport
      const visibleHeight = Math.min(windowHeight - elementTop, elementHeight);
      const scrolledHeight = Math.max(0, windowHeight - elementTop);
      const totalScrollableHeight = elementHeight + windowHeight;
      
      const percent = (scrolledHeight / totalScrollableHeight) * 100;
      setPercentage(Math.min(Math.max(percent, 0), 100));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial calculation

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return {
    ref,
    percentage,
  };
}

/**
 * Hook for viewport-based animations with custom thresholds
 */
export function useViewportAnimation(thresholds: number[] = [0, 0.25, 0.5, 0.75, 1]) {
  const ref = useRef<HTMLElement>(null);
  const [currentThreshold, setCurrentThreshold] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        setIsVisible(entry.isIntersecting);
        
        if (entry.isIntersecting) {
          // Find the highest threshold that's been crossed
          const ratio = entry.intersectionRatio;
          const crossedThreshold = thresholds
            .filter(threshold => ratio >= threshold)
            .pop() || 0;
          
          setCurrentThreshold(crossedThreshold);
        }
      },
      {
        threshold: thresholds,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [thresholds]);

  return {
    ref,
    currentThreshold,
    isVisible,
  };
}
