'use client';

import React, { useEffect, useRef, useState } from 'react';

interface TrailParticle {
  x: number;
  y: number;
  life: number;
  maxLife: number;
  size: number;
  hue: number;
  velocity: { x: number; y: number };
}

interface CursorTrailProps {
  className?: string;
  particleLifespan?: number;
  maxParticles?: number;
}

export const CursorTrail: React.FC<CursorTrailProps> = ({
  className = '',
  particleLifespan = 2000, // 2 seconds in milliseconds
  maxParticles = 50,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<TrailParticle[]>([]);
  const lastMousePos = useRef({ x: 0, y: 0 });
  const lastTime = useRef(0);
  const [isMoving, setIsMoving] = useState(false);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let mouseMovementTimer: NodeJS.Timeout;

    const handleMouseMove = (e: MouseEvent) => {
      const currentTime = Date.now();
      const deltaTime = currentTime - lastTime.current;
      
      // Calculate mouse velocity
      const deltaX = e.clientX - lastMousePos.current.x;
      const deltaY = e.clientY - lastMousePos.current.y;
      const velocity = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / (deltaTime || 1);

      // Only create particles if mouse is moving and within canvas bounds
      const rect = canvas.getBoundingClientRect();
      if (e.clientX >= rect.left && e.clientX <= rect.right && 
          e.clientY >= rect.top && e.clientY <= rect.bottom) {
        
        setIsMoving(true);
        clearTimeout(mouseMovementTimer);
        mouseMovementTimer = setTimeout(() => setIsMoving(false), 100);

        // Create new particles based on movement speed
        const particlesToCreate = Math.min(Math.ceil(velocity * 0.5), 3);
        
        for (let i = 0; i < particlesToCreate; i++) {
          if (particlesRef.current.length < maxParticles) {
            const hueVariation = Math.random() * 60; // 0-60 for purple to blue range
            const baseHue = 260; // Purple base
            
            particlesRef.current.push({
              x: e.clientX - rect.left + (Math.random() - 0.5) * 10,
              y: e.clientY - rect.top + (Math.random() - 0.5) * 10,
              life: 0,
              maxLife: particleLifespan,
              size: 1 + Math.random() * 3,
              hue: baseHue + hueVariation,
              velocity: {
                x: (Math.random() - 0.5) * 2,
                y: (Math.random() - 0.5) * 2,
              },
            });
          }
        }
      }

      lastMousePos.current = { x: e.clientX, y: e.clientY };
      lastTime.current = currentTime;
    };

    const handleTouchMove = (e: TouchEvent) => {
      e.preventDefault();
      const touch = e.touches[0];
      if (touch) {
        const mouseEvent = new MouseEvent('mousemove', {
          clientX: touch.clientX,
          clientY: touch.clientY,
        });
        handleMouseMove(mouseEvent);
      }
    };

    const animate = () => {
      // Clear canvas with slight fade for trail effect
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particlesRef.current = particlesRef.current.filter((particle) => {
        // Update particle
        particle.life += 16; // Approximate 60fps
        particle.x += particle.velocity.x;
        particle.y += particle.velocity.y;

        // Apply slight gravity and friction
        particle.velocity.y += 0.02;
        particle.velocity.x *= 0.99;
        particle.velocity.y *= 0.99;

        // Calculate opacity based on life
        const lifeRatio = particle.life / particle.maxLife;
        const opacity = Math.max(0, 1 - lifeRatio);

        if (opacity > 0) {
          // Create gradient for glow effect
          const gradient = ctx.createRadialGradient(
            particle.x, particle.y, 0,
            particle.x, particle.y, particle.size * 3
          );
          
          gradient.addColorStop(0, `hsla(${particle.hue}, 80%, 70%, ${opacity})`);
          gradient.addColorStop(0.5, `hsla(${particle.hue}, 70%, 60%, ${opacity * 0.5})`);
          gradient.addColorStop(1, `hsla(${particle.hue}, 60%, 50%, 0)`);

          // Draw particle with glow
          ctx.fillStyle = gradient;
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size * 3, 0, Math.PI * 2);
          ctx.fill();

          // Draw bright center
          ctx.fillStyle = `hsla(${particle.hue}, 100%, 80%, ${opacity})`;
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
          ctx.fill();

          // Add sparkle effect for some particles
          if (Math.random() < 0.1) {
            ctx.fillStyle = `hsla(${particle.hue}, 100%, 90%, ${opacity * 0.8})`;
            ctx.beginPath();
            ctx.arc(
              particle.x + (Math.random() - 0.5) * 6,
              particle.y + (Math.random() - 0.5) * 6,
              0.5,
              0,
              Math.PI * 2
            );
            ctx.fill();
          }

          return true; // Keep particle
        }

        return false; // Remove particle
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    // Start animation
    animate();

    // Add event listeners
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('touchmove', handleTouchMove, { passive: false });

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('touchmove', handleTouchMove);
      clearTimeout(mouseMovementTimer);
    };
  }, [particleLifespan, maxParticles]);

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        style={{ 
          mixBlendMode: 'screen',
          zIndex: 10,
        }}
      />
    </div>
  );
};
