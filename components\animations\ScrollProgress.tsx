'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useScrollProgress } from '@/hooks/useScrollAnimation';

export const ScrollProgress: React.FC = () => {
  const { scrollProgress } = useScrollProgress();

  return (
    <motion.div
      className="fixed top-0 left-0 right-0 z-50 h-1 bg-primary/20"
      initial={{ opacity: 0 }}
      animate={{ opacity: scrollProgress > 0 ? 1 : 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className="h-full bg-gradient-to-r from-primary to-primary/80"
        style={{ scaleX: scrollProgress / 100, originX: 0 }}
        transition={{ duration: 0.1, ease: 'easeOut' }}
      />
    </motion.div>
  );
};

export default ScrollProgress;
