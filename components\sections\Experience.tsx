'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, MapPin, Award } from 'lucide-react';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';

interface ExperienceItem {
  title: string;
  company: string;
  location: string;
  period: string;
  description: string[];
  technologies: string[];
}

const experiences: ExperienceItem[] = [
  {
    title: 'Computer Science Student',
    company: 'California Polytechnic University, Pomona',
    location: 'Pomona, CA',
    period: '2020 - 2024',
    description: [
      'Completed Bachelor of Science in Computer Science with focus on software engineering and web development',
      'Gained expertise in data structures, algorithms, and software design patterns',
      'Participated in collaborative projects and hackathons'
    ],
    technologies: ['Java', 'Python', 'C++', 'JavaScript', 'React', 'Node.js']
  }
];

export const Experience: React.FC = () => {
  return (
    <section className="min-h-screen flex items-center justify-center py-20">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={staggerItem} className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                Experience
              </span>
            </h2>
            <p className="text-white/70 text-lg md:text-xl max-w-2xl mx-auto">
              My journey through Computer Science education and professional development
            </p>
          </motion.div>

          {/* Experience Timeline */}
          <motion.div variants={staggerItem} className="space-y-8">
            {experiences.map((exp, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 md:p-8 hover:bg-white/10 transition-all duration-300"
              >
                {/* Timeline dot */}
                <div className="absolute -left-4 top-8 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full border-4 border-slate-900 flex items-center justify-center">
                  <Award className="w-3 h-3 text-slate-900" />
                </div>

                {/* Content */}
                <div className="ml-8">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h3 className="text-xl md:text-2xl font-bold text-white mb-1">
                        {exp.title}
                      </h3>
                      <p className="text-lg text-yellow-400 font-medium">
                        {exp.company}
                      </p>
                    </div>
                    <div className="flex flex-col md:items-end mt-2 md:mt-0">
                      <div className="flex items-center text-white/60 mb-1">
                        <Calendar className="w-4 h-4 mr-2" />
                        <span className="text-sm">{exp.period}</span>
                      </div>
                      <div className="flex items-center text-white/60">
                        <MapPin className="w-4 h-4 mr-2" />
                        <span className="text-sm">{exp.location}</span>
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <ul className="space-y-2 mb-6">
                    {exp.description.map((item, i) => (
                      <li key={i} className="text-white/80 flex items-start">
                        <span className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {exp.technologies.map((tech, i) => (
                      <span
                        key={i}
                        className="px-3 py-1 bg-yellow-400/20 text-yellow-400 rounded-full text-sm font-medium border border-yellow-400/30"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Call to Action */}
          <motion.div variants={staggerItem} className="text-center mt-16">
            <p className="text-white/60 text-lg mb-6">
              Ready to start my professional journey and contribute to innovative projects
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-3 bg-gradient-to-r from-yellow-400 to-orange-400 text-slate-900 font-semibold rounded-lg hover:shadow-lg hover:shadow-yellow-400/25 transition-all duration-300"
            >
              Let's Work Together
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
