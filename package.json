{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "clsx": "^2.1.1", "framer-motion": "^12.19.2", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "next": "15.3.4", "ogl": "^1.0.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}