'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';

interface HyperJumpAnimationProps {
  isActive: boolean;
  onComplete: () => void;
  duration?: number;
}

interface Star {
  x: number;
  y: number;
  z: number;
  size: number;
  speed: number;
}

export const HyperJumpAnimation: React.FC<HyperJumpAnimationProps> = ({
  isActive,
  onComplete,
  duration = 3000,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const starsRef = useRef<Star[]>([]);
  const animationRef = useRef<number>();
  const timelineRef = useRef<gsap.core.Timeline>();
  const [phase, setPhase] = useState<'idle' | 'accelerating' | 'warp' | 'complete'>('idle');

  // Initialize stars
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Reduce star count for better performance
    const starCount = window.innerWidth < 768 ? 100 : 200;
    const stars: Star[] = [];
    for (let i = 0; i < starCount; i++) {
      stars.push({
        x: (Math.random() - 0.5) * 2000,
        y: (Math.random() - 0.5) * 2000,
        z: Math.random() * 1000,
        size: Math.random() * 2 + 0.5,
        speed: Math.random() * 0.5 + 0.1,
      });
    }
    starsRef.current = stars;
  }, []);

  // Animation loop
  useEffect(() => {
    if (!isActive) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let warpSpeed = 0;
    let maxWarpSpeed = 50;
    let acceleration = 0.1;

    const animate = () => {
      ctx.fillStyle = 'rgba(15, 23, 42, 0.1)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      starsRef.current.forEach((star) => {
        // Update star position based on warp speed
        star.z -= warpSpeed * star.speed;

        // Reset star if it goes behind camera
        if (star.z <= 0) {
          star.x = (Math.random() - 0.5) * 2000;
          star.y = (Math.random() - 0.5) * 2000;
          star.z = 1000;
        }

        // Project 3D to 2D
        const x = (star.x / star.z) * 200 + centerX;
        const y = (star.y / star.z) * 200 + centerY;
        const size = (1 - star.z / 1000) * star.size * (warpSpeed / 10 + 1);

        // Only draw stars within canvas bounds
        if (x >= 0 && x <= canvas.width && y >= 0 && y <= canvas.height) {
          // Draw star trail during warp
          if (warpSpeed > 5) {
            const trailLength = warpSpeed * 2;
            const prevX = ((star.x + warpSpeed * star.speed) / (star.z + warpSpeed * star.speed)) * 200 + centerX;
            const prevY = ((star.y + warpSpeed * star.speed) / (star.z + warpSpeed * star.speed)) * 200 + centerY;

            ctx.strokeStyle = `rgba(255, 255, 255, ${Math.min(warpSpeed / 20, 0.8)})`;
            ctx.lineWidth = size;
            ctx.beginPath();
            ctx.moveTo(prevX, prevY);
            ctx.lineTo(x, y);
            ctx.stroke();
          }

          // Draw star
          const opacity = Math.min(warpSpeed / 10 + 0.3, 1);
          ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
          ctx.beginPath();
          ctx.arc(x, y, size, 0, Math.PI * 2);
          ctx.fill();

          // Add glow effect for bright stars
          if (size > 2) {
            ctx.shadowColor = 'rgba(147, 51, 234, 0.5)';
            ctx.shadowBlur = size * 2;
            ctx.beginPath();
            ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
            ctx.fill();
            ctx.shadowBlur = 0;
          }
        }
      });

      // Update warp speed based on phase
      if (phase === 'accelerating' && warpSpeed < maxWarpSpeed) {
        warpSpeed += acceleration;
        acceleration *= 1.05; // Exponential acceleration
      } else if (phase === 'warp') {
        warpSpeed = maxWarpSpeed;
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [isActive, phase]);

  // GSAP Timeline for orchestrating the animation
  useEffect(() => {
    if (!isActive) return;

    const tl = gsap.timeline({
      onComplete: () => {
        setPhase('complete');
        setTimeout(onComplete, 500);
      },
    });

    // Phase 1: Initial acceleration
    tl.to({}, {
      duration: 1,
      onStart: () => setPhase('accelerating'),
    })
    // Phase 2: Warp speed
    .to({}, {
      duration: 1.5,
      onStart: () => setPhase('warp'),
    })
    // Phase 3: Fade out
    .to(canvasRef.current, {
      duration: 0.5,
      opacity: 0,
      ease: 'power2.out',
    });

    timelineRef.current = tl;

    return () => {
      tl.kill();
    };
  }, [isActive, onComplete]);

  if (!isActive) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-slate-900"
      >
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full"
          style={{ background: 'radial-gradient(circle, #1e293b 0%, #0f172a 100%)' }}
        />

        {/* Warp effect overlay */}
        {phase === 'warp' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 pointer-events-none"
          >
            {/* Radial blur effect */}
            <div className="absolute inset-0 bg-gradient-radial from-transparent via-purple-500/10 to-purple-900/30" />
            
            {/* Speed lines */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-px bg-gradient-to-r from-transparent via-white to-transparent"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    height: '2px',
                    transform: `rotate(${Math.random() * 360}deg)`,
                  }}
                  animate={{
                    scaleX: [0, 100, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 0.5,
                    repeat: Infinity,
                    delay: Math.random() * 2,
                    ease: 'easeInOut',
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}

        {/* Center flash effect */}
        {phase === 'warp' && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 2, opacity: [0, 0.5, 0] }}
            transition={{ duration: 2, ease: 'easeOut' }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white rounded-full blur-3xl"
          />
        )}
      </motion.div>
    </AnimatePresence>
  );
};
