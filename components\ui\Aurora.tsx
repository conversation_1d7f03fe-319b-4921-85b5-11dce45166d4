'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface AuroraProps {
  className?: string;
  children?: React.ReactNode;
}

export const Aurora: React.FC<AuroraProps> = ({
  className,
  children,
}) => {
  return (
    <div className={cn('relative w-full h-full', className)}>
      {/* Aurora Background */}
      <div className="absolute inset-0 overflow-hidden bg-black">
        {/* Main Aurora Effect */}
        <div
          className="absolute inset-0 opacity-60"
          style={{
            background: `
              repeating-linear-gradient(
                100deg,
                transparent 0%,
                transparent 5%,
                rgba(147, 51, 234, 0.2) 10%,
                rgba(168, 85, 247, 0.25) 15%,
                rgba(139, 92, 246, 0.2) 20%,
                rgba(124, 58, 237, 0.22) 25%,
                rgba(147, 51, 234, 0.18) 30%,
                transparent 35%
              )
            `,
            backgroundSize: '300% 100%',
            animation: 'aurora-move 45s linear infinite',
            filter: 'blur(1px)',
          }}
        />

        {/* Secondary Aurora Layer */}
        <div
          className="absolute inset-0 opacity-40"
          style={{
            background: `
              repeating-linear-gradient(
                80deg,
                transparent 0%,
                rgba(59, 130, 246, 0.15) 10%,
                rgba(37, 99, 235, 0.18) 20%,
                rgba(29, 78, 216, 0.15) 30%,
                transparent 40%
              )
            `,
            backgroundSize: '200% 100%',
            animation: 'aurora-move 60s linear infinite',
            filter: 'blur(2px)',
          }}
        />

        {/* Tertiary Aurora Layer */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              repeating-linear-gradient(
                120deg,
                transparent 0%,
                rgba(99, 102, 241, 0.12) 15%,
                rgba(79, 70, 229, 0.15) 25%,
                rgba(67, 56, 202, 0.12) 35%,
                transparent 45%
              )
            `,
            backgroundSize: '400% 100%',
            animation: 'aurora-move 75s linear infinite',
            filter: 'blur(3px)',
          }}
        />
      </div>

      {children}
    </div>
  );
};
