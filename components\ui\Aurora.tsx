'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface AuroraProps {
  className?: string;
  children?: React.ReactNode;
}

export const Aurora: React.FC<AuroraProps> = ({
  className,
  children,
}) => {
  return (
    <div className={cn('relative w-full h-full', className)}>
      {/* Aurora Background */}
      <div className="absolute inset-0 overflow-hidden bg-black">
        {/* Main Aurora Effect */}
        <div
          className="absolute inset-0 opacity-80"
          style={{
            background: `
              repeating-linear-gradient(
                100deg,
                transparent 0%,
                transparent 5%,
                rgba(59, 130, 246, 0.3) 10%,
                rgba(165, 180, 252, 0.4) 15%,
                rgba(147, 197, 253, 0.3) 20%,
                rgba(196, 181, 253, 0.35) 25%,
                rgba(96, 165, 250, 0.3) 30%,
                transparent 35%
              )
            `,
            backgroundSize: '300% 100%',
            animation: 'aurora-move 15s linear infinite',
            filter: 'blur(1px)',
          }}
        />

        {/* Secondary Aurora Layer */}
        <div
          className="absolute inset-0 opacity-60"
          style={{
            background: `
              repeating-linear-gradient(
                80deg,
                transparent 0%,
                rgba(139, 92, 246, 0.25) 10%,
                rgba(59, 130, 246, 0.3) 20%,
                rgba(147, 197, 253, 0.25) 30%,
                transparent 40%
              )
            `,
            backgroundSize: '200% 100%',
            animation: 'aurora-move 25s linear infinite reverse',
            filter: 'blur(2px)',
          }}
        />

        {/* Tertiary Aurora Layer */}
        <div
          className="absolute inset-0 opacity-50"
          style={{
            background: `
              repeating-linear-gradient(
                120deg,
                transparent 0%,
                rgba(236, 72, 153, 0.2) 15%,
                rgba(147, 51, 234, 0.25) 25%,
                rgba(59, 130, 246, 0.2) 35%,
                transparent 45%
              )
            `,
            backgroundSize: '400% 100%',
            animation: 'aurora-move 35s linear infinite',
            filter: 'blur(3px)',
          }}
        />
      </div>

      {children}
    </div>
  );
};
