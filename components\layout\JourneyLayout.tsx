'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { WelcomeScreen } from '@/components/sections/WelcomeScreen';
import { HyperJumpAnimation } from '@/components/animations/HyperJumpAnimation';
import { StarFieldNavigation } from '@/components/navigation/StarFieldNavigation';
import { Hero, About, Skills, Projects, Experience, Contact } from '@/components/sections';
import { pageTransition } from '@/lib/animations';
import { cn } from '@/lib/utils';

type JourneyPhase = 'welcome' | 'hyperjump' | 'portfolio';
type PortfolioSection = 'home' | 'about' | 'projects' | 'skills' | 'experience' | 'contact';

interface JourneyLayoutProps {
  children?: React.ReactNode;
  className?: string;
}

export const JourneyLayout: React.FC<JourneyLayoutProps> = ({
  children,
  className = '',
}) => {
  const [phase, setPhase] = useState<JourneyPhase>('welcome');
  const [currentSection, setCurrentSection] = useState<PortfolioSection>('home');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Handle welcome screen enter button
  const handleEnterJourney = () => {
    if (prefersReducedMotion) {
      // Skip animation for users who prefer reduced motion
      setPhase('portfolio');
      setCurrentSection('home');
    } else {
      setPhase('hyperjump');
    }
  };

  // Handle hyper jump completion
  const handleHyperJumpComplete = () => {
    setPhase('portfolio');
    setCurrentSection('home');
  };

  // Handle star navigation
  const handleStarNavigation = (sectionId: string) => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setCurrentSection(sectionId as PortfolioSection);
    
    // Reset transition state after animation
    setTimeout(() => {
      setIsTransitioning(false);
    }, 600);
  };

  // Render current section content
  const renderSectionContent = () => {
    switch (currentSection) {
      case 'home':
        return <Hero />;
      case 'about':
        return <About />;
      case 'skills':
        return <Skills />;
      case 'projects':
        return <Projects />;
      case 'experience':
        return <Experience />;
      case 'contact':
        return <Contact />;
      default:
        return <Hero />;
    }
  };

  return (
    <div className={cn('relative min-h-screen overflow-hidden', className)}>
      {/* Welcome Screen Phase */}
      <AnimatePresence mode="wait">
        {phase === 'welcome' && (
          <WelcomeScreen
            onEnter={handleEnterJourney}
            key="welcome"
          />
        )}
      </AnimatePresence>

      {/* Hyper Jump Animation Phase */}
      <AnimatePresence mode="wait">
        {phase === 'hyperjump' && (
          <HyperJumpAnimation
            isActive={true}
            onComplete={handleHyperJumpComplete}
            key="hyperjump"
          />
        )}
      </AnimatePresence>

      {/* Portfolio Phase */}
      <AnimatePresence mode="wait">
        {phase === 'portfolio' && (
          <motion.div
            key="portfolio"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8 }}
            className="relative min-h-screen"
          >
            {/* Star Field Navigation */}
            <StarFieldNavigation
              onNavigate={handleStarNavigation}
              currentSection={currentSection}
            />

            {/* Main Content Area */}
            <motion.main
              className="relative z-10 min-h-screen"
              variants={pageTransition}
              initial="initial"
              animate="animate"
              exit="exit"
              key={currentSection}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentSection}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ 
                    duration: 0.6, 
                    ease: 'easeOut',
                    delay: isTransitioning ? 0 : 0.2
                  }}
                  className="relative"
                >
                  {renderSectionContent()}
                </motion.div>
              </AnimatePresence>
            </motion.main>

            {/* Background Effects */}
            <div className="fixed inset-0 -z-10">
              {/* Gradient Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/30 to-slate-900" />
              
              {/* Animated Background Elements */}
              <div className="absolute inset-0 overflow-hidden">
                {/* Floating orbs */}
                {[...Array(5)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute rounded-full bg-gradient-to-r from-purple-500/10 to-blue-500/10 blur-xl"
                    style={{
                      width: `${100 + i * 50}px`,
                      height: `${100 + i * 50}px`,
                      left: `${20 + i * 20}%`,
                      top: `${10 + i * 15}%`,
                    }}
                    animate={{
                      x: [0, 30, 0],
                      y: [0, -20, 0],
                      scale: [1, 1.1, 1],
                    }}
                    transition={{
                      duration: 8 + i * 2,
                      repeat: Infinity,
                      ease: 'easeInOut',
                      delay: i * 1.5,
                    }}
                  />
                ))}

                {/* Twinkling stars */}
                {[...Array(50)].map((_, i) => (
                  <motion.div
                    key={`star-${i}`}
                    className="absolute w-px h-px bg-white rounded-full"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                    }}
                    animate={{
                      opacity: [0.2, 1, 0.2],
                      scale: [1, 1.5, 1],
                    }}
                    transition={{
                      duration: 2 + Math.random() * 3,
                      repeat: Infinity,
                      delay: Math.random() * 2,
                      ease: 'easeInOut',
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Section Transition Overlay */}
            <AnimatePresence>
              {isTransitioning && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.3 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="fixed inset-0 z-30 bg-black pointer-events-none"
                />
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Custom styles for removing scrollbars during welcome/hyperjump */}
      <style jsx global>{`
        ${phase !== 'portfolio' ? `
          html, body {
            overflow: hidden !important;
          }
        ` : ''}
      `}</style>
    </div>
  );
};
