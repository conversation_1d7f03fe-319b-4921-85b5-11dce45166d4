'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { ButtonProps } from '@/lib/types';
import { buttonPress, hoverScale } from '@/lib/animations';

const buttonVariants = {
  primary: 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/20',
  secondary: 'bg-secondary text-white hover:bg-secondary/90 focus:ring-secondary/20',
  ghost: 'bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground focus:ring-accent/20',
  link: 'bg-transparent text-primary underline-offset-4 hover:underline focus:ring-primary/20 p-0',
};

const buttonSizes = {
  sm: 'h-9 px-3 text-sm',
  md: 'h-10 px-4 py-2',
  lg: 'h-11 px-8 text-lg',
};

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  href,
  type = 'button',
  className,
  ...props
}) => {
  const baseClasses = cn(
    // Base styles
    'inline-flex items-center justify-center gap-2 rounded-lg font-medium',
    'transition-all duration-300 ease-out',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    // Variant styles
    buttonVariants[variant],
    // Size styles
    variant !== 'link' && buttonSizes[size],
    className
  );

  const MotionComponent = motion.button;

  const buttonContent = (
    <>
      {loading && (
        <motion.div
          className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
        />
      )}
      {children}
    </>
  );

  const motionProps = {
    variants: {
      ...hoverScale,
      ...buttonPress,
    },
    initial: 'rest',
    whileHover: disabled || loading ? 'rest' : 'hover',
    whileTap: disabled || loading ? 'rest' : 'pressed',
    transition: {
      duration: 0.15,
      ease: 'easeOut',
    },
  };

  if (href) {
    return (
      <Link href={href} className={baseClasses} {...props}>
        <motion.span
          className="flex items-center justify-center gap-2"
          {...motionProps}
        >
          {buttonContent}
        </motion.span>
      </Link>
    );
  }

  return (
    <MotionComponent
      type={type}
      className={baseClasses}
      disabled={disabled || loading}
      onClick={onClick}
      {...motionProps}
      {...props}
    >
      {buttonContent}
    </MotionComponent>
  );
};

// Icon Button variant
export const IconButton: React.FC<ButtonProps & { icon: React.ReactNode }> = ({
  icon,
  children,
  size = 'md',
  className,
  ...props
}) => {
  const iconSizes = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
  };

  return (
    <Button
      size={size}
      className={cn(
        'aspect-square p-0',
        iconSizes[size],
        className
      )}
      {...props}
    >
      {icon}
      {children && <span className="sr-only">{children}</span>}
    </Button>
  );
};

// Floating Action Button
export const FloatingButton: React.FC<ButtonProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <motion.div
      className="fixed bottom-8 right-8 z-50"
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0, opacity: 0 }}
      transition={{
        type: 'spring',
        stiffness: 260,
        damping: 20,
      }}
    >
      <Button
        className={cn(
          'h-14 w-14 rounded-full shadow-lg hover:shadow-xl',
          'bg-primary text-white hover:bg-primary/90',
          className
        )}
        {...props}
      >
        {children}
      </Button>
    </motion.div>
  );
};

// Button Group
export const ButtonGroup: React.FC<{
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}> = ({ children, className, orientation = 'horizontal' }) => {
  return (
    <div
      className={cn(
        'flex',
        orientation === 'horizontal' ? 'flex-row' : 'flex-col',
        '[&>*:not(:first-child)]:ml-[-1px] [&>*:not(:last-child)]:rounded-r-none [&>*:not(:first-child)]:rounded-l-none',
        orientation === 'vertical' && '[&>*:not(:first-child)]:mt-[-1px] [&>*:not(:last-child)]:rounded-b-none [&>*:not(:first-child)]:rounded-t-none',
        className
      )}
    >
      {children}
    </div>
  );
};

// Loading Button with custom loading state
export const LoadingButton: React.FC<ButtonProps & {
  loadingText?: string;
}> = ({ children, loading, loadingText, ...props }) => {
  return (
    <Button loading={loading} {...props}>
      <motion.span
        key={loading ? 'loading' : 'default'}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
      >
        {loading ? loadingText || 'Loading...' : children}
      </motion.span>
    </Button>
  );
};

export default Button;
